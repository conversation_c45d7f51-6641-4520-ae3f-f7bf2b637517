"""大模型工具接口层

专门负责：
- 参数校验和转换
- 连接大模型返回的 data_query_params 和内部数据库
- 调用 sqlgen 模块生成 SQL
"""

from datetime import datetime

from .metrics import metrics_registry, TableGranularityManager
from .params import DataQueryParams
from . import sqlgen


def generate_sql(data_query_params: DataQueryParams) -> str:
    """
    使用查询参数生成对应的SQL语句
    
    这是大模型工具的主要接口，负责：
    1. 参数校验和转换
    2. 指标配置查找和验证
    3. 调用 sqlgen 模块生成具体的 SQL
    
    Args:
        data_query_params: 数据查询参数对象
        
    Returns:
        生成的SQL查询语句
        
    Raises:
        ValueError: 当参数无效或指标不支持某些操作时
    """
    # 1. 参数解析和转换
    start_time = datetime.strptime(data_query_params.time_start, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(data_query_params.time_end, "%Y-%m-%d %H:%M:%S")
    
    # 根据时间范围确定数据粒度
    granularity = TableGranularityManager.auto_select_granularity(start_time, end_time)
    
    # 构建过滤条件
    filters = {}
    if data_query_params.appid_filter:
        filters["app_id"] = data_query_params.appid_filter
    if data_query_params.country_filter:
        filters["country"] = data_query_params.country_filter
    
    # 2. 指标类型判断和配置查找
    is_error_distribution = data_query_params.is_error_distribution_metric()
    base_metric_name = data_query_params.get_base_metric_name()
    
    # 获取指标配置
    metric = metrics_registry.get(base_metric_name)
    if not metric:
        raise ValueError(f"未找到指标配置: {base_metric_name}")
    
    # 3. 参数校验
    # 如果是错误码分布指标，验证该指标是否支持错误码分布分析
    if is_error_distribution and not metric.supports_error_distribution:
        raise ValueError(f"指标 {base_metric_name} 不支持错误码分布分析")
    
    # 验证下钻维度是否被指标支持
    if data_query_params.digdimension:
        if not metric.supports_dimension(data_query_params.digdimension):
            available_dims = metric.get_available_dimensions()
            raise ValueError(
                f"指标 {base_metric_name} 不支持维度 {data_query_params.digdimension}。"
                f"可用维度: {', '.join(available_dims)}"
            )
    
    # 4. 准备 SQL 生成所需的参数
    table_name = metric.get_table_name(granularity)
    where_conditions = sqlgen.build_where_conditions(
        metric, start_time, end_time, filters, data_query_params.where
    )
    
    # 5. 根据查询类型调用相应的 SQL 生成函数
    if data_query_params.digdimension:
        # 维度下钻分析
        if is_error_distribution:
            # 错误码分布的维度下钻分析
            return sqlgen.generate_error_distribution_with_dimension_sql(
                metric=metric,
                table_name=table_name,
                where_conditions=where_conditions,
                dimension=data_query_params.digdimension,
                granularity=granularity,
            )
        else:
            # 默认指标的维度下钻分析
            return sqlgen.generate_dimension_trend_sql(
                metric=metric,
                table_name=table_name,
                where_conditions=where_conditions,
                dimension=data_query_params.digdimension,
                granularity=granularity,
            )
    else:
        # 普通趋势分析（无下钻维度）
        if is_error_distribution:
            # 错误码分布分析
            return sqlgen.generate_error_distribution_sql(
                metric=metric,
                table_name=table_name,
                where_conditions=where_conditions,
                granularity=granularity,
            )
        else:
            # 默认指标趋势分析
            return sqlgen.generate_trend_sql(
                metric=metric,
                table_name=table_name,
                where_conditions=where_conditions,
                granularity=granularity,
            )
