"""
SQL模板工具函数模块

保留必要的工具函数，废弃的SQL生成函数已迁移到ZEGOMetric类中
"""

from datetime import datetime

from src.zego_tools.utils.field_mapper import get_db_field_name, get_display_name as get_display_name_func


def get_column_name(dimension_name):
    """从显示名称获取实际列名"""
    return get_db_field_name(dimension_name)


def get_display_name(column_name):
    """从实际列名获取显示名称"""
    return get_display_name_func(column_name)


def format_date(dt: datetime) -> str:
    """格式化日期为年-月-日格式"""
    return dt.strftime("%Y-%m-%d")


# 所有SQL生成函数已迁移到ZEGOMetric类中，此文件仅保留工具函数




