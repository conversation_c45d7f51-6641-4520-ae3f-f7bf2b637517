"""SQL生成模块

专门负责SQL语句的生成，包括：
- WHERE条件构建
- 趋势分析SQL生成
- 维度下钻SQL生成
- 错误码分布SQL生成
"""

from datetime import datetime
from typing import List, Dict, Any
from .metrics import ZEGOMetric


def build_where_conditions(
    metric: ZEGOMetric, 
    start_time: datetime, 
    end_time: datetime, 
    filters: Dict[str, Any], 
    extra_where: str = None
) -> List[str]:
    """
    构建WHERE条件列表
    
    Args:
        metric: 指标对象
        start_time: 开始时间
        end_time: 结束时间
        filters: 过滤条件字典，如 {"app_id": 123, "country": "中国"}
        extra_where: 额外的WHERE条件字符串
        
    Returns:
        WHERE条件列表
    """
    where_conditions = [
        f"{metric.time_field} BETWEEN '{start_time.strftime('%Y-%m-%d %H:%M:%S')}' AND '{end_time.strftime('%Y-%m-%d %H:%M:%S')}'"
    ]

    # 添加指标固有的过滤条件
    if metric.where_filters:
        where_conditions.append(metric.where_filters)

    # 添加用户过滤条件
    for field, value in filters.items():
        where_conditions.append(f"{field} = '{value}'")

    # 添加额外的WHERE条件
    if extra_where:
        where_conditions.append(f"({extra_where})")

    return where_conditions


def build_where_clause(where_conditions: List[str], metric: ZEGOMetric) -> str:
    """
    将WHERE条件列表转换为完整的WHERE子句
    
    Args:
        where_conditions: WHERE条件列表
        metric: 指标对象（用于添加指标特有的过滤条件）
        
    Returns:
        完整的WHERE子句字符串
    """
    where_clause = " AND ".join(where_conditions)
    if metric.where_filters:
        where_clause += f"\n AND {metric.where_filters}"
    return where_clause


def generate_trend_sql(
    metric: ZEGOMetric,
    table_name: str, 
    where_conditions: List[str], 
    granularity: str = "1day"
) -> str:
    """
    生成趋势分析的SQL
    
    Args:
        metric: 指标对象
        table_name: 表名
        where_conditions: WHERE条件列表
        granularity: 时间粒度
        
    Returns:
        趋势分析SQL
    """
    where_clause = " AND ".join(where_conditions)
    if metric.where_filters:
        where_clause += f"\n AND {metric.where_filters}"

    sql = f"""
SELECT
    {metric.time_field},
    {metric.value_sql} as metric_value,
    SUM({metric.sample_ct_field}) as total_count
FROM {table_name}
WHERE {where_clause}
GROUP BY {metric.time_field}
ORDER BY {metric.time_field}
    """
    return sql


def generate_dimension_trend_sql(
    metric: ZEGOMetric,
    table_name: str, 
    where_conditions: List[str], 
    dimension: str, 
    granularity: str = "1day"
) -> str:
    """
    生成维度下钻趋势分析的SQL
    
    Args:
        metric: 指标对象
        table_name: 表名
        where_conditions: WHERE条件列表
        dimension: 下钻维度（如country, sdk_version等）
        granularity: 时间粒度
        
    Returns:
        维度下钻趋势分析SQL
    """
    where_clause = " AND ".join(where_conditions)
    if metric.where_filters:
        where_clause += f"\n AND {metric.where_filters}"

    # 使用CTE优化查询并限制维度数量
    sql = f"""
WITH daily_stats AS (
    SELECT
        {metric.time_field},
        {dimension},
        {metric.value_sql} as metric_value,
        SUM({metric.sample_ct_field}) as total_count
    FROM {table_name}
    WHERE {where_clause}
    GROUP BY {metric.time_field}, {dimension}
),
top_dimensions AS (
    SELECT
        {dimension},
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY {dimension}
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.{metric.time_field},
    ds.{dimension},
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.{dimension} = td.{dimension}
ORDER BY ds.{metric.time_field}, td.dimension_total DESC
    """
    return sql


def generate_error_distribution_sql(
    metric: ZEGOMetric,
    table_name: str, 
    where_conditions: List[str], 
    granularity: str = "1day"
) -> str:
    """
    生成错误码分布分析的SQL
    
    Args:
        metric: 指标对象
        table_name: 表名
        where_conditions: WHERE条件列表
        granularity: 时间粒度
        
    Returns:
        错误码分布分析SQL
    """
    if not metric.supports_error_distribution:
        raise ValueError(f"指标 {metric.name} 不支持错误码分布分析")

    where_clause = " AND ".join(where_conditions)
    if metric.where_filters:
        where_clause += f"\n AND {metric.where_filters}"

    sql = f"""
WITH error_stats AS (
    SELECT
        {metric.time_field},
        {metric.error_field} as error_code,
        SUM({metric.sample_ct_field}) as error_code_count,
        round(SUM({metric.sample_ct_field}) * 100.0 / SUM(SUM({metric.sample_ct_field})) OVER (PARTITION BY {metric.time_field}), 2) as error_percentage,
        SUM(SUM({metric.sample_ct_field})) OVER (PARTITION BY {metric.time_field}) as total_error_count
    FROM {table_name}
    WHERE {where_clause}
    GROUP BY {metric.time_field}, {metric.error_field}
)
SELECT
    {metric.time_field},
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc
    """
    return sql


def generate_error_distribution_with_dimension_sql(
    metric: ZEGOMetric,
    table_name: str,
    where_conditions: List[str],
    dimension: str,
    granularity: str = "1day"
) -> str:
    """
    生成带维度下钻的错误码分布分析SQL

    Args:
        metric: 指标对象
        table_name: 表名
        where_conditions: WHERE条件列表
        dimension: 下钻维度（如country, sdk_version等）
        granularity: 时间粒度

    Returns:
        带维度下钻的错误码分布分析SQL
    """
    if not metric.supports_error_distribution:
        raise ValueError(f"指标 {metric.name} 不支持错误码分布分析")

    where_clause = " AND ".join(where_conditions)
    if metric.where_filters:
        where_clause += f"\n AND {metric.where_filters}"

    sql = f"""
WITH error_stats AS (
    SELECT
        {metric.time_field}, {dimension},
        {metric.error_field} as error_code,
        SUM({metric.sample_ct_field}) as error_code_count,
        round(SUM({metric.sample_ct_field}) * 100.0 / SUM(SUM({metric.sample_ct_field})) OVER (PARTITION BY {metric.time_field}, {dimension}), 2) as error_percentage,
        SUM(SUM({metric.sample_ct_field})) OVER (PARTITION BY {metric.time_field}, {dimension}) as total_error_count
    FROM {table_name}
    WHERE {where_clause}
    GROUP BY {metric.time_field}, {metric.error_field}, {dimension}
)
SELECT
    {metric.time_field}, {dimension},
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc
    """
    return sql
