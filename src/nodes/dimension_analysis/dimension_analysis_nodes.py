from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Command

from src.nodes.dimension_analysis.base_dimension_node import BaseDimensionNode
from src.nodes.dimension_analysis.prompts import universal_analysis_prompt_template
from src.state import State
from src.zego_tools import DataQueryParams


async def worker_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """
    通用分析节点，接受query_param参数进行分析
    从state中获取current_query_params作为分析参数
    """
    query_params = state.get("current_query_params")
    if not query_params:
        raise ValueError("未找到current_query_params，无法执行数据查询分析")

    # 使用统一的通用分析模板，支持所有类型的分析
    return await BaseDimensionNode(
        prompt_template=universal_analysis_prompt_template, query_params=query_params
    ).execute(state, config, store=store)
