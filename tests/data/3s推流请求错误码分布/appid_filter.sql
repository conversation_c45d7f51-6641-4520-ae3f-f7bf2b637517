-- 3s推流请求错误码分布 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 24 个 SQL 语句

-- 3716_3s推流请求错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3720_3s推流请求错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3724_3s推流请求错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3728_3s推流请求错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3732_3s推流请求错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3736_3s推流请求错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3740_3s推流请求错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3744_3s推流请求错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3748_3s推流请求错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3752_3s推流请求错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3756_3s推流请求错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3760_3s推流请求错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3764_3s推流请求错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3768_3s推流请求错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3772_3s推流请求错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3776_3s推流请求错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3826_3s推流请求错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3828_3s推流请求错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3830_3s推流请求错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3832_3s推流请求错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3834_3s推流请求错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3836_3s推流请求错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3838_3s推流请求错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3840_3s推流请求错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

