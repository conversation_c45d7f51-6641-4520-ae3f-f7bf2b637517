-- 3s拉流请求成功率 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 3 个 SQL 语句

-- 230_3s拉流请求成功率_默认指标_1day_AppID=3575801176_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12102001,12301004,52001105,52001104) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 232_3s拉流请求成功率_默认指标_1day_AppID=3575801176_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12102001,12301004,52001105,52001104) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 234_3s拉流请求成功率_默认指标_1day_AppID=3575801176_Dig=country_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12102001,12301004,52001105,52001104) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

