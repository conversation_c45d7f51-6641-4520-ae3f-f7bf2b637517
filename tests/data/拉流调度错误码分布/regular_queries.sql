-- 拉流调度错误码分布 - 常规查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 12 个 SQL 语句

-- 793_拉流调度错误码分布_错误码分布_1day_NoFilter
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 795_拉流调度错误码分布_错误码分布_1day_Country=中国
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国'
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 797_拉流调度错误码分布_错误码分布_1day_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND (platform='native')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 799_拉流调度错误码分布_错误码分布_1day_Country=中国_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 801_拉流调度错误码分布_错误码分布_1day_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 803_拉流调度错误码分布_错误码分布_1day_Country=中国_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 811_拉流调度错误码分布_错误码分布_1day_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 812_拉流调度错误码分布_错误码分布_1day_Country=中国_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国'
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 813_拉流调度错误码分布_错误码分布_1day_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND (platform='native')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 814_拉流调度错误码分布_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 815_拉流调度错误码分布_错误码分布_1day_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 816_拉流调度错误码分布_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

