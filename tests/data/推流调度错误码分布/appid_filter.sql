-- 推流调度错误码分布 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 32 个 SQL 语句

-- 4356_推流调度错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4360_推流调度错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4364_推流调度错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4368_推流调度错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4372_推流调度错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4376_推流调度错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4380_推流调度错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4384_推流调度错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4388_推流调度错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4392_推流调度错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4396_推流调度错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4400_推流调度错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4404_推流调度错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4408_推流调度错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4412_推流调度错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4416_推流调度错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4436_推流调度错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4440_推流调度错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4444_推流调度错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4448_推流调度错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4452_推流调度错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4456_推流调度错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4460_推流调度错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4464_推流调度错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4466_推流调度错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4468_推流调度错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4470_推流调度错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4472_推流调度错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4474_推流调度错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4476_推流调度错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4478_推流调度错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 4480_推流调度错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

