-- 推流rtc子事件错误码分布 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 24 个 SQL 语句

-- 3844_推流rtc子事件错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3848_推流rtc子事件错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3852_推流rtc子事件错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3856_推流rtc子事件错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3860_推流rtc子事件错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3864_推流rtc子事件错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3868_推流rtc子事件错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3872_推流rtc子事件错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3876_推流rtc子事件错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3880_推流rtc子事件错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3884_推流rtc子事件错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3888_推流rtc子事件错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3892_推流rtc子事件错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3896_推流rtc子事件错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3900_推流rtc子事件错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3904_推流rtc子事件错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3954_推流rtc子事件错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3956_推流rtc子事件错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3958_推流rtc子事件错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3960_推流rtc子事件错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国'
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3962_推流rtc子事件错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3964_推流rtc子事件错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3966_推流rtc子事件错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3968_推流rtc子事件错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国' AND (platform='native')
 AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

