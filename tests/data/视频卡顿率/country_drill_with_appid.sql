-- 视频卡顿率 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 8 个 SQL 语句

-- 578_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3575801176'
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 580_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3575801176'
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 582_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3575801176'
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 584_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3575801176'
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 586_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3575801176' AND (platform='native')
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 588_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3575801176' AND (platform='native')
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 590_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3575801176' AND (platform='native')
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 592_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3575801176' AND (platform='native')
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

