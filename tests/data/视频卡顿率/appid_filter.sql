-- 视频卡顿率 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 32 个 SQL 语句

-- 516_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 520_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 524_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 528_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 532_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 536_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 540_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 544_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 548_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 552_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 556_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 560_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 564_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 568_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 572_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 576_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 596_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 600_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 604_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 608_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 612_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 616_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 620_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 624_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 626_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 628_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 630_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 632_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 634_视频卡顿率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND country = '中国' AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 636_视频卡顿率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND country = '中国' AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 638_视频卡顿率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND country = '中国' AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 640_视频卡顿率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

