-- 视频卡顿率 - 常规查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 12 个 SQL 语句

-- 097_视频卡顿率_默认指标_1day_NoFilter
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW()
GROUP BY timestamp
ORDER BY timestamp

-- 099_视频卡顿率_默认指标_1day_Country=中国
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国'
GROUP BY timestamp
ORDER BY timestamp

-- 101_视频卡顿率_默认指标_1day_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 103_视频卡顿率_默认指标_1day_Country=中国_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 105_视频卡顿率_默认指标_1day_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND (platform='native' AND sdk_version!='xxx')
GROUP BY timestamp
ORDER BY timestamp

-- 107_视频卡顿率_默认指标_1day_Country=中国_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
    SUM(video_duration_sum) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
GROUP BY timestamp
ORDER BY timestamp

-- 115_视频卡顿率_默认指标_1day_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW()
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 116_视频卡顿率_默认指标_1day_Country=中国_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 117_视频卡顿率_默认指标_1day_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 118_视频卡顿率_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 119_视频卡顿率_默认指标_1day_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND (platform='native' AND sdk_version!='xxx')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 120_视频卡顿率_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

