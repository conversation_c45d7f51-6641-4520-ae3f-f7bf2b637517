-- 拉流调度成功率 - 常规查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 12 个 SQL 语句

-- 337_拉流调度成功率_默认指标_1day_NoFilter
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 339_拉流调度成功率_默认指标_1day_Country=中国
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国'
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 341_拉流调度成功率_默认指标_1day_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND (platform='native')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 343_拉流调度成功率_默认指标_1day_Country=中国_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 345_拉流调度成功率_默认指标_1day_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 347_拉流调度成功率_默认指标_1day_Country=中国_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 355_拉流调度成功率_默认指标_1day_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 356_拉流调度成功率_默认指标_1day_Country=中国_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国'
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 357_拉流调度成功率_默认指标_1day_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND (platform='native')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 358_拉流调度成功率_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 359_拉流调度成功率_默认指标_1day_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 360_拉流调度成功率_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

