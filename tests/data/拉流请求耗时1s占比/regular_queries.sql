-- 拉流请求耗时1s占比 - 常规查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 12 个 SQL 语句

-- 457_拉流请求耗时1s占比_默认指标_1day_NoFilter
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 459_拉流请求耗时1s占比_默认指标_1day_Country=中国
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 461_拉流请求耗时1s占比_默认指标_1day_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 463_拉流请求耗时1s占比_默认指标_1day_Country=中国_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 465_拉流请求耗时1s占比_默认指标_1day_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 467_拉流请求耗时1s占比_默认指标_1day_Country=中国_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 475_拉流请求耗时1s占比_默认指标_1day_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 476_拉流请求耗时1s占比_默认指标_1day_Country=中国_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 477_拉流请求耗时1s占比_默认指标_1day_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 478_拉流请求耗时1s占比_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 479_拉流请求耗时1s占比_默认指标_1day_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 480_拉流请求耗时1s占比_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

