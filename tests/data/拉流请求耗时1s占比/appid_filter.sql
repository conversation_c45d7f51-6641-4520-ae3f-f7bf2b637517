-- 拉流请求耗时1s占比 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 16 个 SQL 语句

-- 2436_拉流请求耗时1s占比_1min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2440_拉流请求耗时1s占比_10min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2444_拉流请求耗时1s占比_1hour_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2448_拉流请求耗时1s占比_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2452_拉流请求耗时1s占比_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2456_拉流请求耗时1s占比_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2460_拉流请求耗时1s占比_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2464_拉流请求耗时1s占比_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2546_拉流请求耗时1s占比_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2548_拉流请求耗时1s占比_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2550_拉流请求耗时1s占比_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2552_拉流请求耗时1s占比_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2554_拉流请求耗时1s占比_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2556_拉流请求耗时1s占比_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2558_拉流请求耗时1s占比_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2560_拉流请求耗时1s占比_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

