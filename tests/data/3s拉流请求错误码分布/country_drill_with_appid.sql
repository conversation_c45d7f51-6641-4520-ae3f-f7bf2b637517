-- 3s拉流请求错误码分布 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 8 个 SQL 语句

-- 3650_3s拉流请求错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3652_3s拉流请求错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3654_3s拉流请求错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3656_3s拉流请求错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3658_3s拉流请求错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3660_3s拉流请求错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3662_3s拉流请求错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3664_3s拉流请求错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

