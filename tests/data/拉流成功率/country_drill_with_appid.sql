-- 拉流成功率 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 8 个 SQL 语句

-- 066_拉流成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 068_拉流成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 070_拉流成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 072_拉流成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 074_拉流成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 076_拉流成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 078_拉流成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 080_拉流成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

