-- 推流请求耗时均值 - AppID过滤查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 6 个 SQL 语句

-- 434_推流请求耗时均值_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 436_推流请求耗时均值_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 438_推流请求耗时均值_默认指标_1day_AppID=3575801176_Where=platformeqnative
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 440_推流请求耗时均值_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 442_推流请求耗时均值_默认指标_1day_AppID=3575801176_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 444_推流请求耗时均值_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

