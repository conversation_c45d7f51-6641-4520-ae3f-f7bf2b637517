-- 推流请求耗时均值 - 国家下钻查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 3 个 SQL 语句

-- 445_推流请求耗时均值_默认指标_1day_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 447_推流请求耗时均值_默认指标_1day_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 449_推流请求耗时均值_默认指标_1day_Dig=country_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

