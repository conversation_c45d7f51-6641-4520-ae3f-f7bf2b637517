-- 推流调度成功率 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 3 个 SQL 语句

-- 374_推流调度成功率_默认指标_1day_AppID=3575801176_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3575801176'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 376_推流调度成功率_默认指标_1day_AppID=3575801176_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3575801176' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 378_推流调度成功率_默认指标_1day_AppID=3575801176_Dig=country_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

