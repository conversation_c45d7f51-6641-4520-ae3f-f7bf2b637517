-- 推流调度成功率 - AppID过滤查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 6 个 SQL 语句

-- 362_推流调度成功率_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3575801176'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 364_推流调度成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 366_推流调度成功率_默认指标_1day_AppID=3575801176_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3575801176' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 368_推流调度成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 370_推流调度成功率_默认指标_1day_AppID=3575801176_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

-- 372_推流调度成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY timestamp
ORDER BY timestamp

