-- 3s推流请求成功率 - AppID过滤查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 6 个 SQL 语句

-- 242_3s推流请求成功率_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY timestamp
ORDER BY timestamp

-- 244_3s推流请求成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY timestamp
ORDER BY timestamp

-- 246_3s推流请求成功率_默认指标_1day_AppID=3575801176_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY timestamp
ORDER BY timestamp

-- 248_3s推流请求成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY timestamp
ORDER BY timestamp

-- 250_3s推流请求成功率_默认指标_1day_AppID=3575801176_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY timestamp
ORDER BY timestamp

-- 252_3s推流请求成功率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY timestamp
ORDER BY timestamp

