-- 推流请求耗时1s占比 - 常规查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 12 个 SQL 语句

-- 481_推流请求耗时1s占比_默认指标_1day_NoFilter
SELECT
    timestamp,
    sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 483_推流请求耗时1s占比_默认指标_1day_Country=中国
SELECT
    timestamp,
    sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 485_推流请求耗时1s占比_默认指标_1day_Where=platformeqnative
SELECT
    timestamp,
    sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 487_推流请求耗时1s占比_默认指标_1day_Country=中国_Where=platformeqnative
SELECT
    timestamp,
    sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 489_推流请求耗时1s占比_默认指标_1day_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 491_推流请求耗时1s占比_默认指标_1day_Country=中国_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
    SUM(pub_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 499_推流请求耗时1s占比_默认指标_1day_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 500_推流请求耗时1s占比_默认指标_1day_Country=中国_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 501_推流请求耗时1s占比_默认指标_1day_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 502_推流请求耗时1s占比_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 503_推流请求耗时1s占比_默认指标_1day_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 504_推流请求耗时1s占比_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

