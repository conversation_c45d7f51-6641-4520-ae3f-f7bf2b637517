-- 推流请求耗时1s占比 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 8 个 SQL 语句

-- 2626_推流请求耗时1s占比_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2628_推流请求耗时1s占比_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2630_推流请求耗时1s占比_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2632_推流请求耗时1s占比_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2634_推流请求耗时1s占比_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2636_推流请求耗时1s占比_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2638_推流请求耗时1s占比_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2640_推流请求耗时1s占比_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

