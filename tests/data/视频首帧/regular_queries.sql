-- 视频首帧 - 常规查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 12 个 SQL 语句

-- 385_视频首帧_默认指标_1day_NoFilter
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame'
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 387_视频首帧_默认指标_1day_Country=中国
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国'
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 389_视频首帧_默认指标_1day_Where=platformeqnative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 391_视频首帧_默认指标_1day_Country=中国_Where=platformeqnative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 393_视频首帧_默认指标_1day_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 395_视频首帧_默认指标_1day_Country=中国_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 403_视频首帧_默认指标_1day_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 404_视频首帧_默认指标_1day_Country=中国_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 405_视频首帧_默认指标_1day_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 406_视频首帧_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 407_视频首帧_默认指标_1day_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 408_视频首帧_默认指标_1day_Country=中国_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

