-- 视频首帧 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 32 个 SQL 语句

-- 2052_视频首帧_1min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 2056_视频首帧_10min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 2060_视频首帧_1hour_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 2064_视频首帧_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 2068_视频首帧_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 2072_视频首帧_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 2076_视频首帧_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 2080_视频首帧_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 2084_视频首帧_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2088_视频首帧_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2092_视频首帧_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2096_视频首帧_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2100_视频首帧_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2104_视频首帧_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2108_视频首帧_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2112_视频首帧_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2132_视频首帧_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2136_视频首帧_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2140_视频首帧_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2144_视频首帧_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2148_视频首帧_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2152_视频首帧_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2156_视频首帧_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2160_视频首帧_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2162_视频首帧_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2164_视频首帧_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2166_视频首帧_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2168_视频首帧_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国'
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2170_视频首帧_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2172_视频首帧_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2174_视频首帧_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2176_视频首帧_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

