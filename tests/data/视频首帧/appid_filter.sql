-- 视频首帧 - AppID过滤查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 6 个 SQL 语句

-- 386_视频首帧_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3575801176'
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 388_视频首帧_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 390_视频首帧_默认指标_1day_AppID=3575801176_Where=platformeqnative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3575801176' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 392_视频首帧_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 394_视频首帧_默认指标_1day_AppID=3575801176_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

-- 396_视频首帧_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
    SUM(fft_cnt) as total_count
FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'sdk_play_decode_first_video_frame'
GROUP BY timestamp
ORDER BY timestamp

