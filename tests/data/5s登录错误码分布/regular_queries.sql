-- 5s登录错误码分布 - 常规查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 12 个 SQL 语句

-- 577_5s登录错误码分布_错误码分布_1day_NoFilter
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 579_5s登录错误码分布_错误码分布_1day_Country=中国
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 581_5s登录错误码分布_错误码分布_1day_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 583_5s登录错误码分布_错误码分布_1day_Country=中国_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 585_5s登录错误码分布_错误码分布_1day_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND (platform='native' AND sdk_version!='xxx')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 587_5s登录错误码分布_错误码分布_1day_Country=中国_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 595_5s登录错误码分布_错误码分布_1day_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 596_5s登录错误码分布_错误码分布_1day_Country=中国_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 597_5s登录错误码分布_错误码分布_1day_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 598_5s登录错误码分布_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 599_5s登录错误码分布_错误码分布_1day_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND (platform='native' AND sdk_version!='xxx')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 600_5s登录错误码分布_错误码分布_1day_Country=中国_Dig=app_id_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

