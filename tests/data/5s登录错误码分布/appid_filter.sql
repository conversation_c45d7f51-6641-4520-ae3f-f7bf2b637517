-- 5s登录错误码分布 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 32 个 SQL 语句

-- 3076_5s登录错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3080_5s登录错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3084_5s登录错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3088_5s登录错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3092_5s登录错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3096_5s登录错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3100_5s登录错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3104_5s登录错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3108_5s登录错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3112_5s登录错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3116_5s登录错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3120_5s登录错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3124_5s登录错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3128_5s登录错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3132_5s登录错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3136_5s登录错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, sdk_version,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, sdk_version) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, sdk_version
)
SELECT
    timestamp, sdk_version,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3156_5s登录错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3160_5s登录错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3164_5s登录错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3168_5s登录错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3172_5s登录错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3176_5s登录错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3180_5s登录错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3184_5s登录错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, isp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, isp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, isp
)
SELECT
    timestamp, isp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3186_5s登录错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3188_5s登录错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3190_5s登录错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3192_5s登录错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3194_5s登录错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3196_5s登录错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3198_5s登录错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 3200_5s登录错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, app_id,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, app_id) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, error, app_id
)
SELECT
    timestamp, app_id,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

