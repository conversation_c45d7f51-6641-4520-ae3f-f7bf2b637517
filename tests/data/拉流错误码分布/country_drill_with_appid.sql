-- 拉流错误码分布 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 8 个 SQL 语句

-- 2882_拉流错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 2884_拉流错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 2886_拉流错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 2888_拉流错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 2890_拉流错误码分布_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 2892_拉流错误码分布_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 2894_拉流错误码分布_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 2896_拉流错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

