-- 拉流错误码分布 - AppID过滤查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 6 个 SQL 语句

-- 530_拉流错误码分布_错误码分布_1day_AppID=3575801176
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 532_拉流错误码分布_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 534_拉流错误码分布_错误码分布_1day_AppID=3575801176_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 536_拉流错误码分布_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 538_拉流错误码分布_错误码分布_1day_AppID=3575801176_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 540_拉流错误码分布_错误码分布_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp) as total_error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY timestamp, error
)
SELECT
    timestamp,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

