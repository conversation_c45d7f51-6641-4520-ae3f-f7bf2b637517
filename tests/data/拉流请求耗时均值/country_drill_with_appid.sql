-- 拉流请求耗时均值 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 8 个 SQL 语句

-- 2242_拉流请求耗时均值_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2244_拉流请求耗时均值_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2246_拉流请求耗时均值_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2248_拉流请求耗时均值_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2250_拉流请求耗时均值_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2252_拉流请求耗时均值_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2254_拉流请求耗时均值_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2256_拉流请求耗时均值_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3575801176' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

