-- 拉流请求耗时均值 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 16 个 SQL 语句

-- 2180_拉流请求耗时均值_1min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2184_拉流请求耗时均值_10min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2188_拉流请求耗时均值_1hour_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2192_拉流请求耗时均值_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2196_拉流请求耗时均值_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2200_拉流请求耗时均值_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2204_拉流请求耗时均值_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2208_拉流请求耗时均值_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    sum(play_request_num)/sum(play_request_cnt) as metric_value,
    SUM(play_request_cnt) as total_count
FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
GROUP BY timestamp
ORDER BY timestamp

-- 2290_拉流请求耗时均值_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2292_拉流请求耗时均值_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2294_拉流请求耗时均值_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2296_拉流请求耗时均值_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国'
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2298_拉流请求耗时均值_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2300_拉流请求耗时均值_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2302_拉流请求耗时均值_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2304_拉流请求耗时均值_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND (platform='native')
 AND coalesce(extlib_type,0) = 0
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

