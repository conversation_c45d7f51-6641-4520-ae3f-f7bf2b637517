-- 端到端丢包率 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 32 个 SQL 语句

-- 2692_端到端丢包率_1min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 2696_端到端丢包率_10min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 2700_端到端丢包率_1hour_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 2704_端到端丢包率_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 2708_端到端丢包率_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 2712_端到端丢包率_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 2716_端到端丢包率_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 2720_端到端丢包率_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 2724_端到端丢包率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2728_端到端丢包率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2732_端到端丢包率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2736_端到端丢包率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2740_端到端丢包率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2744_端到端丢包率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2748_端到端丢包率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2752_端到端丢包率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2772_端到端丢包率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2776_端到端丢包率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2780_端到端丢包率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2784_端到端丢包率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2788_端到端丢包率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2792_端到端丢包率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2796_端到端丢包率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2800_端到端丢包率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2802_端到端丢包率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2804_端到端丢包率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2806_端到端丢包率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2808_端到端丢包率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国'
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2810_端到端丢包率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND country = '中国' AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2812_端到端丢包率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND country = '中国' AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2814_端到端丢包率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND country = '中国' AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 2816_端到端丢包率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND (platform='native')
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

