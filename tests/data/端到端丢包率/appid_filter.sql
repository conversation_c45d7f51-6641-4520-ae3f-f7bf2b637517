-- 端到端丢包率 - AppID过滤查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 6 个 SQL 语句

-- 506_端到端丢包率_默认指标_1day_AppID=3575801176
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3575801176'
GROUP BY timestamp
ORDER BY timestamp

-- 508_端到端丢包率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯'
GROUP BY timestamp
ORDER BY timestamp

-- 510_端到端丢包率_默认指标_1day_AppID=3575801176_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3575801176' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 512_端到端丢包率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
GROUP BY timestamp
ORDER BY timestamp

-- 514_端到端丢包率_默认指标_1day_AppID=3575801176_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
GROUP BY timestamp
ORDER BY timestamp

-- 516_端到端丢包率_默认指标_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative_AND_sdk_version!eqxxx
SELECT
    timestamp,
    ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
    SUM(peer_to_peer_plr_cnt) as total_count
FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native' AND sdk_version!='xxx')
GROUP BY timestamp
ORDER BY timestamp

