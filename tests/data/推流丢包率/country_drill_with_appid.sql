-- 推流丢包率 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 3 个 SQL 语句

-- 158_推流丢包率_默认指标_1day_AppID=3575801176_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3575801176'
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 160_推流丢包率_默认指标_1day_AppID=3575801176_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3575801176' AND (platform='native')
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 162_推流丢包率_默认指标_1day_AppID=3575801176_Dig=country_Where=platformeqnative_AND_sdk_version!eqxxx
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

