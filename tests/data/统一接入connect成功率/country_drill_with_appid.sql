-- 统一接入connect成功率 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 8 个 SQL 语句

-- 1090_统一接入connect成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 1092_统一接入connect成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 1094_统一接入connect成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 1096_统一接入connect成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 1098_统一接入connect成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 1100_统一接入connect成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 1102_统一接入connect成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

-- 1104_统一接入connect成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=country_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        country,
        ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, country
),
top_dimensions AS (
    SELECT
        country,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY country
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.country,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.country = td.country
ORDER BY ds.timestamp, td.dimension_total DESC

