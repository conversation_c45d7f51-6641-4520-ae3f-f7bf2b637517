-- 5s登录成功率 - AppID过滤查询
-- 生成时间: 2025-08-05 18:29:43
-- 总计 32 个 SQL 语句

-- 260_5s登录成功率_1min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY timestamp
ORDER BY timestamp

-- 264_5s登录成功率_10min_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY timestamp
ORDER BY timestamp

-- 268_5s登录成功率_1hour_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY timestamp
ORDER BY timestamp

-- 272_5s登录成功率_1day_AppID=3206531758_Country=沙特阿拉伯
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY timestamp
ORDER BY timestamp

-- 276_5s登录成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY timestamp
ORDER BY timestamp

-- 280_5s登录成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY timestamp
ORDER BY timestamp

-- 284_5s登录成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY timestamp
ORDER BY timestamp

-- 288_5s登录成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Where=platformeqnative
SELECT
    timestamp,
    ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
    SUM(err_cnt) as total_count
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY timestamp
ORDER BY timestamp

-- 292_5s登录成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 296_5s登录成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 300_5s登录成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 304_5s登录成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 308_5s登录成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 312_5s登录成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 316_5s登录成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 320_5s登录成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=sdk_version_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        sdk_version,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, sdk_version
),
top_dimensions AS (
    SELECT
        sdk_version,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY sdk_version
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.sdk_version,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
ORDER BY ds.timestamp, td.dimension_total DESC

-- 340_5s登录成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 344_5s登录成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 348_5s登录成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 352_5s登录成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 356_5s登录成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 360_5s登录成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 364_5s登录成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 368_5s登录成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=isp_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        isp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '3206531758' AND country = '沙特阿拉伯' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, isp
),
top_dimensions AS (
    SELECT
        isp,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY isp
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.isp,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.isp = td.isp
ORDER BY ds.timestamp, td.dimension_total DESC

-- 370_5s登录成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 372_5s登录成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 374_5s登录成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 376_5s登录成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国'
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 378_5s登录成功率_1min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 380_5s登录成功率_10min_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 100 MINUTE) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 382_5s登录成功率_1hour_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1h
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 HOUR) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

-- 384_5s登录成功率_1day_AppID=3206531758_Country=沙特阿拉伯_Dig=app_id_Where=platformeqnative
WITH daily_stats AS (
    SELECT
        timestamp,
        app_id,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND (platform='native')
 AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY timestamp, app_id
),
top_dimensions AS (
    SELECT
        app_id,
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY app_id
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.timestamp,
    ds.app_id,
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.app_id = td.app_id
ORDER BY ds.timestamp, td.dimension_total DESC

