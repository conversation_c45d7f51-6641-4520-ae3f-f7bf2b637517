-- 拉流l3子事件错误码分布 - 指定AppID的国家下钻查询
-- 生成时间: 2025-08-05 18:14:52
-- 总计 3 个 SQL 语句

-- 782_拉流l3子事件错误码分布_错误码分布_1day_AppID=3575801176_Dig=country
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176'
 AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 784_拉流l3子事件错误码分布_错误码分布_1day_AppID=3575801176_Dig=country_Where=platformeqnative
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native')
 AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

-- 786_拉流l3子事件错误码分布_错误码分布_1day_AppID=3575801176_Dig=country_Where=platformeqnative_AND_sdk_version!eqxxx
WITH error_stats AS (
    SELECT
        timestamp, country,
        error as error_code,
        SUM(err_cnt) as error_code_count,
        round(SUM(err_cnt) * 100.0 / SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country), 2) as error_percentage,
        SUM(SUM(err_cnt)) OVER (PARTITION BY timestamp, country) as total_error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '3575801176' AND (platform='native' AND sdk_version!='xxx')
 AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY timestamp, error, country
)
SELECT
    timestamp, country,
    error_code,
    error_code_count,
    error_percentage
FROM error_stats
WHERE error_percentage >= 0.1
ORDER BY timestamp desc, total_error_count desc, error_code_count desc

